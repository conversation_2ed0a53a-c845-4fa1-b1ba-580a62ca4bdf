<?php

use App\Events\NotificationsEvent;
use App\Http\Controllers\pages\HomeController;
use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

// Include front-end routes
require __DIR__ . '/front.php';

// Include admin routes
require __DIR__ . '/admin.php';

require __DIR__ . '/auth.php';
Route::get('/dashboard/refresh', [HomeController::class, 'refreshDashboard'])->name('dashboard.refresh');

Route::get('/test-notification', function () {
    NotificationsEvent::dispatch('Hello World');
    return 'Notification sent';
});

Route::get('/robots.txt', function () {
    $sitemapUrl = config('app.url') . '/sitemap.xml';

    $content = <<<EOT
    User-agent: *
    Disallow: /admin
    Disallow: /telescope
    Disallow: /horizon
    Disallow: /log-viewer
    Disallow: /queue
    Disallow: /profile
    Disallow: /short-urls
    Disallow: /uploads
    Disallow: /srt
    Disallow: /email/verify
    Disallow: /Reset-Pass
    Disallow: /notifications

    # Allow public routes
    Allow: /
    Allow: /categories
    Allow: /category
    Allow: /search
    Allow: /product
    Allow: /seller
    Allow: /about
    Allow: /contact
    Allow: /blog
    Allow: /page

    Sitemap: {$sitemapUrl}
    EOT;

    return response($content, 200, ['Content-Type' => 'text/plain']);
})->name('robots.txt');

Route::get('/sitemap.xml', function () {
    $sitemap = Sitemap::create()
        // Home page
        ->add(Url::create('/'))

        // Static pages
        ->add(Url::create('/about'))
        ->add(Url::create('/contact'))

        // Marketplace pages
        ->add(Url::create('/categories'))
        ->add(Url::create('/search'))

        // Blog
        ->add(Url::create('/blog'));

    // Add dynamic pages from database
    // Categories
    $categories = \App\Models\Category::where('status', true)->get();
    foreach ($categories as $category) {
        $sitemap->add(Url::create('/category/' . $category->slug));
    }

    // Static pages from database
    $pages = \App\Models\Page::where('status', true)->get();
    foreach ($pages as $page) {
        $sitemap->add(Url::create('/page/' . $page->slug));
    }

    // Blog posts
    if (class_exists('Modules\Blog\Models\Blog')) {
        $blogs = \Modules\Blog\Models\Blog::where('is_published', true)->get();
        foreach ($blogs as $blog) {
            $sitemap->add(Url::create('/blog/' . $blog->slug));
        }
    }

    // Products (limit to approved products, max 1000 for performance)
    $products = \App\Models\Product::where('status', \App\Enums\StatusEnum::APPROVED->value)->orderBy('created_at', 'desc')->limit(1000)->get();
    foreach ($products as $product) {
        $sitemap->add(Url::create('/product/' . $product->slug));
    }

    // Seller profiles (only include users with approved products)
    $sellers = \App\Models\User::whereHas('products', function ($query) {
        $query->where('status', \App\Enums\StatusEnum::APPROVED->value);
    })
        ->limit(500)
        ->get();

    foreach ($sellers as $seller) {
        $sitemap->add(Url::create('/seller/' . $seller->username));
    }

    return $sitemap->toResponse(request());
});
