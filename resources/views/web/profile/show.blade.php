@extends('web.layouts.layout')
@section('title', __('الملف الشخصي'))

@section('page-meta')
    <meta name="description" content="{{ __('الملف الشخصي في موقع حراجي') }}">
    <meta name="keywords" content="{{ __('حراجي, ملف شخصي, إعلانات, بيع, شراء') }}">
@endsection

@section('content')
    <main class="py-8">
        <div class="container mx-auto px-4">
            <!-- Breadcrumbs -->
            <div class="text-sm breadcrumbs mb-6">
                <ul>
                    <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                    <li>{{ __('الملف الشخصي') }}</li>
                </ul>
            </div>

            <!-- Main Split Layout -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
               
                <!-- Right Half - Profile Information -->
                <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden h-fit ">
                    <!-- Cover Photo -->
                    <div class="relative h-48 bg-gradient-to-r from-primary/20 to-secondary/20">
                        @if ($user->cover)
                            <img src="{{ asset('storage/' . $user->cover) }}" alt="{{ $user->name }}"
                                class="w-full h-full object-cover">
                        @else
                            <div
                                class="w-full h-full bg-gradient-to-r from-primary/20 to-secondary/20 flex items-center justify-center">
                                <i class="fas fa-image text-4xl text-base-content/30"></i>
                            </div>
                        @endif

                        <!-- Edit Profile Button -->
                        <a href="{{ route('web.profile.edit') }}"
                            class="absolute top-3 right-3 rtl:right-auto rtl:left-3 btn btn-sm btn-primary">
                            <i class="fas fa-edit mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('تعديل') }}
                        </a>
                    </div>

                    <!-- Profile Info -->
                    <div class="relative px-6 pt-12 pb-6">
                        <!-- Avatar -->
                        <div class="absolute -top-8 left-6 rtl:left-auto rtl:right-6">
                            <div class="avatar">
                                <div
                                    class="w-16 h-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                                    @if ($user->sessions->count() > 0 && $user->avatar())
                                        <img src="{{ $user->avatar() }}" alt="{{ $user->name }}">
                                    @else
                                        <img src="{{ asset('assets/default-avatar.png') }}" alt="{{ $user->name }}">
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- User Info -->
                        <div class="mb-4">
                            <h1 class="text-xl font-bold mb-1">{{ $user->name }}</h1>
                            <p class="text-sm text-base-content/70">{{ __('عضو منذ') }}
                                {{ $user->created_at->format('Y-m-d') }}</p>

                            <!-- Rating Display -->
                            @if ($user->ratings_count > 0)
                                <div class="flex items-center gap-2 mt-2">
                                    <div class="rating rating-sm">
                                        @for ($i = 1; $i <= 5; $i++)
                                            <input type="radio" class="mask mask-star-2 bg-orange-400" disabled
                                                {{ $i <= round($user->average_rating) ? 'checked' : '' }} />
                                        @endfor
                                    </div>
                                    <span class="text-sm text-base-content/70">({{ $user->ratings_count }}
                                        {{ __('تقييم') }})</span>
                                </div>
                            @endif
                        </div>

                        <!-- Status Badges -->
                        <div class="flex flex-wrap gap-2 mb-4">
                            @if ($user->email_verified_at)
                                <div class="badge badge-success badge-sm gap-1">
                                    <i class="fas fa-check-circle text-xs"></i> {{ __('البريد مؤكد') }}
                                </div>
                            @else
                                <div class="badge badge-warning badge-sm gap-1">
                                    <i class="fas fa-exclamation-circle text-xs"></i> {{ __('البريد غير مؤكد') }}
                                </div>
                            @endif

                            @if ($user->isVerified())
                                <div class="badge badge-primary badge-sm gap-1">
                                    <i class="fas fa-shield-check text-xs"></i> {{ __('الحساب موثق') }}
                                </div>
                            @elseif($user->hasPendingVerification())
                                <div class="badge badge-info badge-sm gap-1">
                                    <i class="fas fa-clock text-xs"></i> {{ __('طلب التوثيق قيد المراجعة') }}
                                </div>
                            @endif
                        </div>

                        <!-- Contact Info -->
                        <div class="space-y-2 text-sm mb-4">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-envelope text-primary text-xs"></i>
                                <span>{{ $user->email }}</span>
                            </div>
                            @if ($user->phone)
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-phone-alt text-primary text-xs"></i>
                                    <span>{{ $user->phone }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Bio -->
                        @if ($user->sessions->count() > 0 && $user->sessions->first()->bio)
                            <div class="mb-4 pb-4 border-b border-base-300">
                                <h3 class="font-semibold mb-2 text-sm">{{ __('نبذة') }}</h3>
                                <p class="text-sm text-base-content/80">{{ $user->sessions->first()->bio }}</p>
                            </div>
                        @endif

                        <!-- Action Buttons -->
                        <div class="space-y-3 mb-4">
                            <div class="grid grid-cols-2 gap-3">
                                <a href="{{ route('web.profile.products') }}" class="action-btn">
                                    <i class="fas fa-tag text-primary"></i>
                                    <span>{{ __('إعلاناتي') }} ({{ $user->products->count() }})</span>
                                </a>

                                <a href="{{ route('web.profile.favorites') }}" class="action-btn">
                                    <i class="fas fa-heart text-red-500"></i>
                                    <span>{{ __('المفضلة') }} ({{ $user->favorites->count() }})</span>
                                </a>

                                <a href="{{ route('web.profile.followers') }}" class="action-btn">
                                    <i class="fas fa-users text-blue-500"></i>
                                    <span>{{ __('المتابعين') }} ({{ $user->followers->count() }})</span>
                                </a>

                                <a href="{{ route('web.profile.following') }}" class="action-btn">
                                    <i class="fas fa-user-plus text-green-500"></i>
                                    <span>{{ __('أتابعهم') }} ({{ $user->follows()->where('followable_type', 'App\\Models\\User')->count() }})</span>
                                </a>
                            </div>
                        </div>

                        <!-- Achievements Section -->
                        @if ($user->achievements->count() > 0)
                            <div class="border-t border-base-300 pt-4">
                                <h3 class="font-semibold mb-3 text-sm">{{ __('الإنجازات') }}</h3>
                                <div class="grid grid-cols-3 gap-2">
                                    @foreach ($user->achievements->take(6) as $achievement)
                                        <div class="achievement-badge" title="{{ $achievement->description }}">
                                            <div class="text-center p-2 bg-base-200 rounded-lg hover:bg-primary/10 transition-colors">
                                                @if ($achievement->icon)
                                                    <i class="{{ $achievement->icon }} text-lg text-primary mb-1"></i>
                                                @else
                                                    <i class="fas fa-trophy text-lg text-primary mb-1"></i>
                                                @endif
                                                <div class="text-xs font-medium">{{ $achievement->name }}</div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                @if ($user->achievements->count() > 6)
                                    <div class="text-center mt-3">
                                        <button class="btn btn-xs btn-outline">{{ __('عرض المزيد') }} ({{ $user->achievements->count() - 6 }})</button>
                                    </div>
                                @endif
                            </div>
                        @endif

                        <!-- Verification Request Button -->
                        @if (!$user->isVerified() && !$user->hasPendingVerification() && $user->email_verified_at)
                            <div class="mt-4 pt-4 border-t border-base-300">
                                <a href="{{ route('web.verification.form') }}"
                                    class="btn btn-outline btn-primary btn-sm w-full gap-2">
                                    <i class="fas fa-shield-check"></i>
                                    {{ __('طلب التوثيق') }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                 <!-- Left Half - User Products -->
                <div class="bg-base-100 rounded-xl shadow-lg overflow-hidden h-fit">
                    <div class="p-6 border-b border-base-300">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-bold">{{ __('إعلاناتي') }}</h2>
                            <a href="{{ route('web.profile.products') }}" class="btn btn-sm btn-outline btn-primary">
                                {{ __('عرض الكل') }}
                            </a>
                        </div>
                    </div>

                    <!-- Products Container with Max Height -->
                    <div class="h-[526px] overflow-y-auto">
                  
                        @if ($user->products->count() > 0)
                            <div class="p-4">
                                @livewire('products', [
                                    'pageType' => 'profile',
                                    'filters' => [
                                        'sort' => 'newest',
                                    ],
                                    'showCategories' => false,
                                    'showFilters' => false,
                                    'perPage' => 6,
                                ])
                            </div>
                        @else
                            <div class="text-center py-12">
                                <i class="fas fa-tag text-4xl text-base-content/30 mb-4"></i>
                                <p class="text-base mb-4">{{ __('لا توجد إعلانات حتى الآن') }}</p>
                                @if ($user->hasVerifiedEmail())
                                    <a href="{{ route('web.products.create') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus mr-2 rtl:ml-2 rtl:mr-0"></i>
                                        {{ __('إضافة إعلان جديد') }}
                                    </a>
                                @else
                                    <a href="{{ route('web.auth.verification.notice') }}"
                                        class="btn btn-warning btn-sm">
                                        <i class="fas fa-exclamation-triangle mr-2 rtl:ml-2 rtl:mr-0"></i>
                                        {{ __('تحقق من البريد') }}
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

            </div>
        </div>
    </main>
@endsection

@section('page-style')
    <style>
        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background-color: hsl(var(--b2));
            border: 1px solid hsl(var(--b3));
            border-radius: 0.5rem;
            text-decoration: none;
            color: hsl(var(--bc));
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .action-btn:hover {
            background-color: hsl(var(--p) / 0.1);
            border-color: hsl(var(--p) / 0.3);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px hsl(var(--b3));
            color: hsl(var(--bc));
            text-decoration: none;
        }

        .achievement-badge:hover {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        /* Custom scrollbar for better UX */
        .max-h-96::-webkit-scrollbar {
            width: 6px;
        }

        .max-h-96::-webkit-scrollbar-track {
            background: hsl(var(--b2));
            border-radius: 3px;
        }

        .max-h-96::-webkit-scrollbar-thumb {
            background: hsl(var(--b3));
            border-radius: 3px;
        }

        .max-h-96::-webkit-scrollbar-thumb:hover {
            background: hsl(var(--bc) / 0.3);
        }

        /* Ensure both columns have same height */
        .h-fit {
            height: fit-content;
        }

        /* Make sure grid items align properly */
        @media (min-width: 1024px) {
            .lg\:grid-cols-2 > div {
                align-self: start;
            }
        }
    </style>
@endsection
